<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite: Recreate users table to adjust CHECK/enum-like constraint safely
            Schema::dropIfExists('users_temp');
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email');
                $table->string('username')->nullable();
                $table->enum('role', ['admin', 'member', 'user'])->default('member');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                // Use unique index names to avoid collision with previous migrations
                $table->unique(['email'], 'users_member_email_unique');
                $table->unique(['username'], 'users_member_username_unique');
                $table->index(['role'], 'users_member_role_index');
                $table->index(['failed_login_attempts'], 'users_member_failed_login_attempts_index');
            });

            // Copy data, mapping 'member' -> 'member'
            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT id, name, email, username,
                       CASE WHEN role = 'member' THEN 'member' ELSE role END AS role,
                       email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at
                FROM users
            ");

            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } elseif ($driver === 'pgsql') {
            // Update existing data first to ensure compatibility
            DB::table('users')->where('role', 'member')->update(['role' => 'member']);

            // Clean up any existing types from previous migrations to make this idempotent
            try {
                DB::statement('DROP TYPE IF EXISTS user_role_member CASCADE');
                DB::statement('DROP TYPE IF EXISTS user_role_new CASCADE');
            } catch (Throwable $e) {
                // Ignore errors - types might not exist
            }

            // Create new ENUM type with updated values
            DB::statement("CREATE TYPE user_role_member AS ENUM ('admin', 'member', 'user')");

            // Add a temporary column with the new type
            DB::statement("ALTER TABLE users ADD COLUMN role_new user_role_member DEFAULT 'member'");

            // Copy data from current column to new column (cast via text)
            DB::statement('UPDATE users SET role_new = role::text::user_role_member');

            // Drop default on old column and remove it
            DB::statement('ALTER TABLE users ALTER COLUMN role DROP DEFAULT');
            DB::statement('ALTER TABLE users DROP COLUMN role');

            // Rename the new column to role
            DB::statement('ALTER TABLE users RENAME COLUMN role_new TO role');

            // Drop the old type if it still exists under common names
            // Note: It may have a different name; ignore errors if not found
            try {
                DB::statement('DROP TYPE IF EXISTS user_role');
                DB::statement('DROP TYPE IF EXISTS user_role_old');
            } catch (Throwable $e) {
                // ignore
            }
        } else { // MySQL / MariaDB
            // Update data then modify enum definition and default
            DB::table('users')->where('role', 'member')->update(['role' => 'member']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'member', 'user') NOT NULL DEFAULT 'member'");
        }
    }

    public function down(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // SQLite: Recreate table to revert to 'member'
            Schema::dropIfExists('users_temp');
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email');
                $table->string('username')->nullable();
                $table->enum('role', ['admin', 'member', 'user'])->default('member');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                // Use unique index names to avoid collision with previous migrations
                $table->unique(['email'], 'users_member_email_unique');
                $table->unique(['username'], 'users_member_username_unique');
                $table->index(['role'], 'users_member_role_index');
                $table->index(['failed_login_attempts'], 'users_member_failed_login_attempts_index');
            });

            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT id, name, email, username,
                       CASE WHEN role = 'member' THEN 'member' ELSE role END AS role,
                       email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at
                FROM users
            ");

            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } elseif ($driver === 'pgsql') {
            // Update data back to 'member' where currently 'member'

            // Clean up any existing types from previous migrations to make this idempotent
            try {
                DB::statement('DROP TYPE IF EXISTS user_role_member CASCADE');
                DB::statement('DROP TYPE IF EXISTS user_role_old CASCADE');
            } catch (Throwable $e) {
                // Ignore errors - types might not exist
            }

            // Create old enum type
            DB::statement("CREATE TYPE user_role_member AS ENUM ('admin', 'member', 'user')");

            // Add temp column with old type
            DB::statement("ALTER TABLE users ADD COLUMN role_old user_role_member DEFAULT 'member'");

            // Copy data (cast via text), mapping member -> member prior to cast
            DB::statement("UPDATE users SET role_old = (CASE WHEN role::text = 'member' THEN 'member' ELSE role::text END)::user_role_member");

            // Drop current role column and rename back
            DB::statement('ALTER TABLE users DROP COLUMN role');
            DB::statement('ALTER TABLE users RENAME COLUMN role_old TO role');

            // Clean up the type we created in the up() method
            try {
                DB::statement('DROP TYPE IF EXISTS user_role_member CASCADE');
            } catch (Throwable $e) {
                // ignore
            }
        } else { // MySQL
            DB::table('users')->where('role', 'member')->update(['role' => 'member']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'member', 'user') NOT NULL DEFAULT 'member'");
        }
    }
};
