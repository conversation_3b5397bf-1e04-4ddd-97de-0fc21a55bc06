<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Field;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // ///////////// pa wak si e field ta under maintenance
        $fields = Field::all();

        foreach ($fields as $field) {
            $field->updateStatusBasedOnDates();
        }
        // ////////////////////////////////////////////////////////////////////
        $query = Field::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('type', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $fields = $query->withCount('activeBookings')
            ->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc') // Secondary sort for deterministic ordering
            ->paginate(15);

        return view('admin.fields.index', compact('fields'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {

        return view('admin.fields.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', 'unique:fields,name'],
            'icon' => ['nullable', 'string', 'max:255'],
            'type' => ['required', Rule::in(array_keys(Field::getFieldTypes()))],
            'description' => ['nullable', 'string', 'max:1000'],
            'hourly_rate' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'night_hourly_rate' => ['nullable', 'numeric', 'min:0', 'max:9999.99'],
            'night_time_start' => ['nullable', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'capacity' => ['required', 'integer', 'min:1', 'max:1000'],
            'status' => ['required', Rule::in(array_keys(Field::getStatuses()))],
            'amenities' => ['nullable', 'array'],
            'amenities.*' => ['exists:amenities,id'],
            'utilities' => ['nullable', 'array'],
            'utilities.*' => ['exists:utilities,id'],
            'min_booking_hours' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'max_booking_hours' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'begin_date' => ['nullable', 'date', 'after_or_equal:today'],
            'end_date' => ['nullable', 'date', 'after_or_equal:begin_date'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        Log::info('Logging start_date:', ['begin_date' => $request->start_date]);
        /*$field = Field::create([
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'hourly_rate' => $request->hourly_rate,
            'night_hourly_rate' => $request->night_hourly_rate,
            'night_time_start' => $request->night_time_start ?: '18:00',
            'capacity' => $request->capacity,
            'status' => $request->status,
            'min_booking_hours' => $request->min_booking_hours,
            'max_booking_hours' => $request->max_booking_hours,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]); */

        if ($request->begin_date && $request->end_date) {
            // Both dates are filled, create record with dates
            $field = Field::create([
                'name' => $request->name,
                'icon' => $request->icon,
                'type' => $request->type,
                'description' => $request->description,
                'hourly_rate' => $request->hourly_rate,
                'night_hourly_rate' => $request->night_hourly_rate,
                'night_time_start' => $request->night_time_start ?: '18:00',
                'capacity' => $request->capacity,
                'status' => 'Under Maintenance',
                'min_booking_hours' => $request->min_booking_hours,
                'max_booking_hours' => $request->max_booking_hours,
                'start_date' => $request->begin_date,
                'end_date' => $request->end_date,
            ]);
        } else {
            // One or both dates are missing, create record without dates (or with nulls)
            $field = Field::create([
                'name' => $request->name,
                'icon' => $request->icon,
                'type' => $request->type,
                'description' => $request->description,
                'hourly_rate' => $request->hourly_rate,
                'night_hourly_rate' => $request->night_hourly_rate,
                'night_time_start' => $request->night_time_start ?: '18:00',
                'capacity' => $request->capacity,
                'status' => $request->status,
                'min_booking_hours' => $request->min_booking_hours,
                'max_booking_hours' => $request->max_booking_hours,
                'start_date' => null,
                'end_date' => null,
            ]);
        }

        // Sync amenities relationship
        $field->amenities()->sync($request->amenities ?? []);

        // Sync utilities relationship
        $field->utilities()->sync($request->utilities ?? []);

        return redirect()->route('admin.fields.index')
            ->with('success', 'Field created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Field $field)
    {
        $field->load([
            'bookings' => function ($query) {
                $query->with('user')->orderBy('booking_date', 'desc')->limit(10);
            },
            'amenities',
            'utilities',
        ]);

        return view('admin.fields.show', compact('field'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Field $field)
    {
        // Load the relationships needed for the edit form
        $field->load(['amenities', 'utilities']);

        return view('admin.fields.edit', compact('field'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Field $field)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', Rule::unique('fields', 'name')->ignore($field->id)],
            'icon' => ['nullable', 'string', 'max:255'],
            'type' => ['required', Rule::in(array_keys(Field::getFieldTypes()))],
            'description' => ['nullable', 'string', 'max:1000'],
            'hourly_rate' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'night_hourly_rate' => ['nullable', 'numeric', 'min:0', 'max:9999.99'],
            'night_time_start' => ['nullable', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'capacity' => ['required', 'integer', 'min:1', 'max:1000'],
            'status' => ['required', Rule::in(array_keys(Field::getStatuses()))],
            'amenities' => ['nullable', 'array'],
            'amenities.*' => ['exists:amenities,id'],
            'utilities' => ['nullable', 'array'],
            'utilities.*' => ['exists:utilities,id'],
            'min_booking_hours' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'max_booking_hours' => ['required', 'numeric', 'min:0', 'max:9999.99'],
            'begin_date' => ['nullable', 'date', 'after_or_equal:today'],
            'end_date' => ['nullable', 'date', 'after_or_equal:begin_date'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        /* $field->update([
             'name' => $request->name,
             'type' => $request->type,
             'description' => $request->description,
             'hourly_rate' => $request->hourly_rate,
             'night_hourly_rate' => $request->night_hourly_rate,
             'night_time_start' => $request->night_time_start ?: '18:00',
             'capacity' => $request->capacity,
             'status' => $request->status,
             'min_booking_hours' => $request->min_booking_hours,
             'max_booking_hours' => $request->max_booking_hours,
         ]);*/

        if ($request->begin_date && $request->end_date) {
            // Both dates are filled, update record with dates
            $field->update([
                'name' => $request->name,
                'icon' => $request->icon,
                'type' => $request->type,
                'description' => $request->description,
                'hourly_rate' => $request->hourly_rate,
                'night_hourly_rate' => $request->night_hourly_rate,
                'night_time_start' => $request->night_time_start ?: '18:00',
                'capacity' => $request->capacity,
                'status' => 'Under Maintenance',
                'min_booking_hours' => $request->min_booking_hours,
                'max_booking_hours' => $request->max_booking_hours,
                'start_date' => $request->begin_date,
                'end_date' => $request->end_date,
            ]);
        } else {
            // One or both dates are missing, create record without dates (or with nulls)
            $field->update([
                'name' => $request->name,
                'icon' => $request->icon,
                'type' => $request->type,
                'description' => $request->description,
                'hourly_rate' => $request->hourly_rate,
                'night_hourly_rate' => $request->night_hourly_rate,
                'night_time_start' => $request->night_time_start ?: '18:00',
                'capacity' => $request->capacity,
                'status' => $request->status,
                'min_booking_hours' => $request->min_booking_hours,
                'max_booking_hours' => $request->max_booking_hours,
                'start_date' => null,
                'end_date' => null,
            ]);
        }

        // Sync amenities relationship
        $field->amenities()->sync($request->amenities ?? []);

        // Sync utilities relationship
        $field->utilities()->sync($request->utilities ?? []);

        return redirect()->route('admin.fields.show', $field)
            ->with('success', 'Field updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Field $field)
    {
        // Check if field has active bookings
        $activeBookingsCount = $field->activeBookings()->count();

        if ($activeBookingsCount > 0) {
            return back()->with('error',
                "Cannot delete field '{$field->name}' because it has {$activeBookingsCount} active booking(s). Please cancel or complete all bookings first.");
        }

        $fieldName = $field->name;
        $field->delete(); // Soft delete

        return redirect()->route('admin.fields.index')
            ->with('success', "Field '{$fieldName}' has been deleted successfully.");
    }
}
